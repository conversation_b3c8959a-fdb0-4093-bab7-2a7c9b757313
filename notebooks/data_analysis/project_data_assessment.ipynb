from google.colab import drive
drive.mount("/content/drive", force_remount=True)

!pip install pandas numpy

import os
import pandas as pd
import numpy as np

# Thesis constraints
THESIS_SIZE_LIMIT_GB = 10

# Complete project data inventory - all available data types
projects_data = {
    'Project': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ani <PERSON>', '<PERSON>', 'RPC<PERSON>', 'RES', 'Trino'],
    'Location': ['Italy', 'Spain', 'Italy', 'USA', 'USA', 'USA', 'Italy'],
    'Full_Name': [
        '<PERSON><PERSON><PERSON>',
        '<PERSON>djar - ENEL',
        '<PERSON><PERSON> - <PERSON>',
        'Sunstreams Project - McCarthy',
        'Althea - RPCS',
        'Nortan - RES Renewables',
        'Trino - ENEL'
    ],
    'Point_Cloud_URL': [
        's3://preetam-filezilla-test/Castro/Pointcloud/',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_pointcloud.las',
        's3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/',
        's3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/',
        's3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/',
        'To Be Keyed In'
    ],
    'CAD_URL': [
        's3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/',
        's3://ftp-enel/mudejar-spain/',
        's3://ftp-enel/pian_di_di_giorgio-italy/',
        's3://ftp-mccarthy/CAD Files/',
        's3://ftp-rpcs/Althea/CAD Files/',
        's3://preetam-filezilla-test/RES_Renewable/BLOCK_11/CAD/',
        'To Be Keyed In'
    ],
    'Ortho_URL': [
        's3://preetam-filezilla-test/Castro/Ortho/',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_Ortho.tif',
        's3://preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/',
        's3://preetam-filezilla-test/RCPS/Updated_031024/Ortho/',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2025/RES/Block11/RSE_Block11_ortho.tif',
        'To Be Keyed In'
    ],
    'IFC_Available': ['No', 'No', 'No', 'No', 'No', 'No','Yes'],
    'CAD_Available': ['Yes', 'Yes', 'Yes', 'Yes', 'Yes', 'Yes','No']
}

df = pd.DataFrame(projects_data)


notebook_path = "/content/drive/MyDrive/Colab Notebooks/asbuilt-foundation-analysis/data_analysis/project_inventory.csv"
df.to_csv(notebook_path, index=False)

!ls /content/drive/MyDrive/Colab\ Notebooks/asbuilt-foundation-analysis/data_analysis/

# Summary information
print("=" * 60)
print(f"Total Projects: {len(df)}")
print(f"Data Types Available: Point Cloud, CAD, Orthomosaic")
print(f"IFC Files Available: {df['IFC_Available'].value_counts().to_dict()}")
print(f"CAD Files Available: {df['CAD_Available'].value_counts().to_dict()}")

print("=" * 60)

# Display the full table in tabular format
df  # This renders the full dataframe as an HTML table in Colab

!ls /content/drive/MyDrive/thesis_datasets_20250613/

!ls -ltra /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/

!ls -ltra /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/pointcloud/

!ls -ltra /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/cad/

!ls -ltra /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/orthomosaic

def get_file_info(path):
    entries = []
    for root, _, files in os.walk(path):
        for file in files:
            full_path = os.path.join(root, file)
            size_mb = round(os.path.getsize(full_path) / (1024 * 1024), 2)
            extension = os.path.splitext(file)[1].lower()

            # Extract project name based on known structure
            # path/.../{data_type}/{project}/...
            relative_parts = os.path.relpath(full_path, path).split(os.sep)
            project = relative_parts[0] if len(relative_parts) > 1 else "Unknown"

            entries.append({
                "Project": project,
                "File Path": full_path,
                "Size (MB)": size_mb,
                "Extension": extension
            })
    return entries

def summarize_directory(path):
    if not os.path.exists(path):
        raise FileNotFoundError(f"Path does not exist: {path}")

    file_info = get_file_info(path)
    return pd.DataFrame(file_info)


def check_extension_availability(df, extension=".dwg"):
    """
    Checks if a given file extension exists in subfolders per project.
    """
    extension = extension.lower()
    df = df.copy()
    df['Extension'] = df['Extension'].str.lower()

    # Identify which projects have the target extension
    projects_with_ext = df[df['Extension'] == extension]['Project'].unique()

    # Generate a per-project availability matrix
    result = (
        df[['Project']]
        .drop_duplicates()
        .assign(**{
            f"has_{extension}": lambda x: x['Project'].isin(projects_with_ext).map({True: 'Yes', False: 'No'})
        })
        .sort_values("Project")
        .reset_index(drop=True)
    )

    return result


pd.set_option('display.max_rows', None)
pd.set_option('display.max_colwidth', None)  # Optional: shows full file paths


base_path = '/content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets'

pd.set_option('display.max_rows', None)
pd.set_option('display.max_colwidth', None)  # Optional: shows full file paths

path = f'{base_path}/pointcloud'
pc_df = summarize_directory(path)

check_extension_availability(pc_df, extension=".las")

display(pc_df)

path = f'{base_path}/cad'
cad_df = summarize_directory(path)

check_extension_availability(cad_df, extension=".dwg")

display(cad_df)

path = f'{base_path}/orthomosaic'
ortho_df = summarize_directory(path)

check_extension_availability(ortho_df, extension=".tif")

check_extension_availability(ortho_df, extension=".tiff")

display(ortho_df)

def create_comprehensive_summary():
    """
    Create a comprehensive summary table from all analysis results.
    """
    
    # Aggregate point cloud data
    pc_summary = pc_df.groupby('Project').agg({
        'Size (MB)': ['sum', 'count'],
        'Extension': lambda x: ', '.join(x.unique())
    }).round(2)
    
    pc_summary.columns = ['PC_Size_MB', 'PC_File_Count', 'PC_Extensions']
    
    # Aggregate CAD data
    cad_summary = cad_df.groupby('Project').agg({
        'Size (MB)': ['sum', 'count'],
        'Extension': lambda x: ', '.join(x.unique())
    }).round(2)
    
    cad_summary.columns = ['CAD_Size_MB', 'CAD_File_Count', 'CAD_Extensions']
    
    # Aggregate orthomosaic data
    ortho_summary = ortho_df.groupby('Project').agg({
        'Size (MB)': ['sum', 'count'],
        'Extension': lambda x: ', '.join(x.unique())
    }).round(2)
    
    ortho_summary.columns = ['Ortho_Size_MB', 'Ortho_File_Count', 'Ortho_Extensions']
    
    # Combine all summaries
    comprehensive_summary = pd.concat([pc_summary, cad_summary, ortho_summary], axis=1, sort=False)
    
    # Fill NaN values with 0 for sizes and counts, 'N/A' for extensions
    size_cols = ['PC_Size_MB', 'CAD_Size_MB', 'Ortho_Size_MB']
    count_cols = ['PC_File_Count', 'CAD_File_Count', 'Ortho_File_Count']
    ext_cols = ['PC_Extensions', 'CAD_Extensions', 'Ortho_Extensions']
    
    comprehensive_summary[size_cols] = comprehensive_summary[size_cols].fillna(0)
    comprehensive_summary[count_cols] = comprehensive_summary[count_cols].fillna(0).astype(int)
    comprehensive_summary[ext_cols] = comprehensive_summary[ext_cols].fillna('N/A')
    
    # Calculate total size per project
    comprehensive_summary['Total_Size_MB'] = (
        comprehensive_summary['PC_Size_MB'] + 
        comprehensive_summary['CAD_Size_MB'] + 
        comprehensive_summary['Ortho_Size_MB']
    ).round(2)
    
    # Convert to GB for readability
    comprehensive_summary['Total_Size_GB'] = (comprehensive_summary['Total_Size_MB'] / 1024).round(2)
    
    # Add data completeness score
    comprehensive_summary['Data_Completeness'] = (
        (comprehensive_summary['PC_File_Count'] > 0).astype(int) +
        (comprehensive_summary['CAD_File_Count'] > 0).astype(int) +
        (comprehensive_summary['Ortho_File_Count'] > 0).astype(int)
    )
    
    # Add accessibility assessment
    accessibility = {
        'castro': 'Good',
        'mccarthy': 'Good', 
        'rpcs': 'Good',
        'res': 'Good',
        'mudjar': 'Limited',
        'giorgio': 'Limited'
    }
    
    comprehensive_summary['Accessibility'] = comprehensive_summary.index.map(accessibility).fillna('Unknown')
    
    # Reorder columns for better readability
    column_order = [
        'Total_Size_GB', 'Data_Completeness', 'Accessibility',
        'PC_Size_MB', 'PC_File_Count', 'PC_Extensions',
        'CAD_Size_MB', 'CAD_File_Count', 'CAD_Extensions', 
        'Ortho_Size_MB', 'Ortho_File_Count', 'Ortho_Extensions'
    ]
    
    return comprehensive_summary[column_order]

# Create and display the comprehensive summary
if 'pc_df' in locals() and 'cad_df' in locals() and 'ortho_df' in locals():
    comprehensive_df = create_comprehensive_summary()
    
    print("📊 COMPREHENSIVE DATA SUMMARY")
    print("=" * 80)
    print(f"Total Projects Analyzed: {len(comprehensive_df)}")
    print(f"Total Data Size: {comprehensive_df['Total_Size_GB'].sum():.2f} GB")
    print(f"Projects with Complete Data (3/3 types): {(comprehensive_df['Data_Completeness'] == 3).sum()}")
    print(f"Projects with Good Accessibility: {(comprehensive_df['Accessibility'] == 'Good').sum()}")
    print("\n")
    
    display(comprehensive_df)
    
    # Save the comprehensive summary
    comprehensive_df.to_csv('/content/drive/MyDrive/Colab Notebooks/asbuilt-foundation-analysis/data_analysis/comprehensive_data_summary.csv')
    print("\n💾 Comprehensive summary saved to: comprehensive_data_summary.csv")
else:
    print("❌ Please run the data analysis sections above first to generate pc_df, cad_df, and ortho_df")

def rank_projects_for_thesis(comprehensive_df, size_limit_gb=10):
    """
    Rank projects based on thesis suitability criteria.
    """
    ranking_df = comprehensive_df.copy()
    
    # Scoring criteria (higher is better)
    ranking_df['Size_Score'] = np.where(
        ranking_df['Total_Size_GB'] <= size_limit_gb, 
        10 - (ranking_df['Total_Size_GB'] / size_limit_gb) * 5,  # 10 to 5 points
        np.maximum(0, 5 - (ranking_df['Total_Size_GB'] - size_limit_gb))  # Penalty for oversized
    ).round(1)
    
    ranking_df['Completeness_Score'] = ranking_df['Data_Completeness'] * 3.33  # 0-10 scale
    
    ranking_df['Accessibility_Score'] = ranking_df['Accessibility'].map({
        'Good': 10,
        'Limited': 5,
        'Poor': 2,
        'Unknown': 0
    })
    
    # Point cloud availability bonus
    ranking_df['PC_Bonus'] = np.where(ranking_df['PC_File_Count'] > 0, 5, 0)
    
    # Calculate total score
    ranking_df['Total_Score'] = (
        ranking_df['Size_Score'] * 0.3 +
        ranking_df['Completeness_Score'] * 0.4 +
        ranking_df['Accessibility_Score'] * 0.2 +
        ranking_df['PC_Bonus'] * 0.1
    ).round(1)
    
    # Add recommendation
    def get_recommendation(row):
        if row['Total_Score'] >= 8.5:
            return "Highly Recommended"
        elif row['Total_Score'] >= 7.0:
            return "Recommended"
        elif row['Total_Score'] >= 5.0:
            return "Conditional"
        else:
            return "Not Recommended"
    
    ranking_df['Recommendation'] = ranking_df.apply(get_recommendation, axis=1)
    
    # Sort by total score
    ranking_df = ranking_df.sort_values('Total_Score', ascending=False)
    
    # Select key columns for display
    display_cols = [
        'Total_Score', 'Recommendation', 'Total_Size_GB', 
        'Data_Completeness', 'Accessibility',
        'PC_File_Count', 'CAD_File_Count', 'Ortho_File_Count'
    ]
    
    return ranking_df[display_cols]

# Create project ranking
if 'comprehensive_df' in locals():
    ranking_df = rank_projects_for_thesis(comprehensive_df)
    
    print("🏆 PROJECT RANKING FOR THESIS SUITABILITY")
    print("=" * 80)
    print("Scoring Criteria:")
    print("• Size Score (30%): Prefer < 10GB, penalty for larger")
    print("• Completeness Score (40%): Points for each data type available")
    print("• Accessibility Score (20%): Good > Limited > Poor access")
    print("• Point Cloud Bonus (10%): Extra points for PC availability")
    print("\n")
    
    display(ranking_df)
    
    # Save ranking
    ranking_df.to_csv('/content/drive/MyDrive/Colab Notebooks/asbuilt-foundation-analysis/data_analysis/project_ranking.csv')
    print("\n💾 Project ranking saved to: project_ranking.csv")
    
    # Print top recommendations
    top_projects = ranking_df[ranking_df['Recommendation'].isin(['Highly Recommended', 'Recommended'])]
    if len(top_projects) > 0:
        print(f"\n🎯 TOP RECOMMENDATIONS ({len(top_projects)} projects):")
        for project, row in top_projects.iterrows():
            print(f"• {project.upper()}: {row['Recommendation']} (Score: {row['Total_Score']})")
else:
    print("❌ Please run the comprehensive summary section above first")