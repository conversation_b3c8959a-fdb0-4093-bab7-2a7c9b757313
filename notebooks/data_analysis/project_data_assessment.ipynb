from google.colab import drive
drive.mount("/content/drive", force_remount=True)

!pip install pandas numpy

import os
import pandas as pd
import numpy as np

# Thesis constraints
THESIS_SIZE_LIMIT_GB = 10

# Complete project data inventory - all available data types
projects_data = {
    'Project': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ani <PERSON>', '<PERSON>', 'RPC<PERSON>', 'RES', 'Trino'],
    'Location': ['Italy', 'Spain', 'Italy', 'USA', 'USA', 'USA', 'Italy'],
    'Full_Name': [
        '<PERSON><PERSON><PERSON>',
        '<PERSON>djar - ENEL',
        '<PERSON><PERSON> - <PERSON>',
        'Sunstreams Project - McCarthy',
        'Althea - RPCS',
        'Nortan - RES Renewables',
        'Trino - ENEL'
    ],
    'Point_Cloud_URL': [
        's3://preetam-filezilla-test/Castro/Pointcloud/',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_pointcloud.las',
        's3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/',
        's3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/',
        's3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/',
        'To Be Keyed In'
    ],
    'CAD_URL': [
        's3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/',
        's3://ftp-enel/mudejar-spain/',
        's3://ftp-enel/pian_di_di_giorgio-italy/',
        's3://ftp-mccarthy/CAD Files/',
        's3://ftp-rpcs/Althea/CAD Files/',
        's3://preetam-filezilla-test/RES_Renewable/BLOCK_11/CAD/',
        'To Be Keyed In'
    ],
    'Ortho_URL': [
        's3://preetam-filezilla-test/Castro/Ortho/',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/Giorgio_Fly12_Ortho.tif',
        's3://preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/',
        's3://preetam-filezilla-test/RCPS/Updated_031024/Ortho/',
        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2025/RES/Block11/RSE_Block11_ortho.tif',
        'To Be Keyed In'
    ],
    'IFC_Available': ['No', 'No', 'No', 'No', 'No', 'No','Yes'],
    'CAD_Available': ['Yes', 'Yes', 'Yes', 'Yes', 'Yes', 'Yes','No']
}

df = pd.DataFrame(projects_data)


notebook_path = "/content/drive/MyDrive/Colab Notebooks/asbuilt-foundation-analysis/data_analysis/project_inventory.csv"
df.to_csv(notebook_path, index=False)

!ls /content/drive/MyDrive/Colab\ Notebooks/asbuilt-foundation-analysis/data_analysis/

# Summary information
print("=" * 60)
print(f"Total Projects: {len(df)}")
print(f"Data Types Available: Point Cloud, CAD, Orthomosaic")
print(f"IFC Files Available: {df['IFC_Available'].value_counts().to_dict()}")
print(f"CAD Files Available: {df['CAD_Available'].value_counts().to_dict()}")

print("=" * 60)

# Display the full table in tabular format
df  # This renders the full dataframe as an HTML table in Colab

!ls /content/drive/MyDrive/thesis_datasets_20250613/

!ls -ltra /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/

!ls -ltra /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/pointcloud/

!ls -ltra /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/cad/

!ls -ltra /content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets/orthomosaic

def get_file_info(path):
    entries = []
    for root, _, files in os.walk(path):
        for file in files:
            full_path = os.path.join(root, file)
            size_mb = round(os.path.getsize(full_path) / (1024 * 1024), 2)
            extension = os.path.splitext(file)[1].lower()

            # Extract project name based on known structure
            # path/.../{data_type}/{project}/...
            relative_parts = os.path.relpath(full_path, path).split(os.sep)
            project = relative_parts[0] if len(relative_parts) > 1 else "Unknown"

            entries.append({
                "Project": project,
                "File Path": full_path,
                "Size (MB)": size_mb,
                "Extension": extension
            })
    return entries

def summarize_directory(path):
    if not os.path.exists(path):
        raise FileNotFoundError(f"Path does not exist: {path}")

    file_info = get_file_info(path)
    return pd.DataFrame(file_info)


def check_extension_availability(df, extension=".dwg"):
    """
    Checks if a given file extension exists in subfolders per project.
    """
    extension = extension.lower()
    df = df.copy()
    df['Extension'] = df['Extension'].str.lower()

    # Identify which projects have the target extension
    projects_with_ext = df[df['Extension'] == extension]['Project'].unique()

    # Generate a per-project availability matrix
    result = (
        df[['Project']]
        .drop_duplicates()
        .assign(**{
            f"has_{extension}": lambda x: x['Project'].isin(projects_with_ext).map({True: 'Yes', False: 'No'})
        })
        .sort_values("Project")
        .reset_index(drop=True)
    )

    return result


pd.set_option('display.max_rows', None)
pd.set_option('display.max_colwidth', None)  # Optional: shows full file paths


base_path = '/content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets'

pd.set_option('display.max_rows', None)
pd.set_option('display.max_colwidth', None)  # Optional: shows full file paths

path = f'{base_path}/pointcloud'
pc_df = summarize_directory(path)

check_extension_availability(pc_df, extension=".las")

display(pc_df)

path = f'{base_path}/cad'
cad_df = summarize_directory(path)

check_extension_availability(cad_df, extension=".dwg")

display(cad_df)

path = f'{base_path}/orthomosaic'
ortho_df = summarize_directory(path)

check_extension_availability(ortho_df, extension=".tif")

check_extension_availability(ortho_df, extension=".tiff")

display(ortho_df)