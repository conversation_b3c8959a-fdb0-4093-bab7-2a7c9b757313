# Install required packages
!pip install laspy pandas numpy matplotlib seaborn

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

try:
    import laspy
    LASPY_AVAILABLE = True
    print("laspy available")
except ImportError:
    LASPY_AVAILABLE = False
    print("laspy not available - install with: pip install laspy")

import laspy
from dataclasses import dataclass, field
from typing import Optional, Dict, Union

@dataclass
class DensityReport:
    file_path: str
    num_points: Optional[int] = None
    area_m2: Optional[float] = None
    density_pts_per_m2: Optional[float] = None
    x_range_m: Optional[float] = None
    y_range_m: Optional[float] = None
    status: str = 'success'
    error: Optional[str] = None

def read_las_file_density(las_file_path: str) -> Union[laspy.LasData, str]:
    try:
        return laspy.read(las_file_path), None
    except Exception as e:
        return None, str(e)

def compute_xy_range(header: laspy.LasHeader) -> Dict[str, float]:
    x_range = header.max[0] - header.min[0]
    y_range = header.max[1] - header.min[1]
    return {'x_range': x_range, 'y_range': y_range}

def compute_density(num_points: int, area: float) -> float:
    return num_points / area if area > 0 else 0.0

def analyze_point_density(las_file_path: str) -> DensityReport:
    las, error = read_las_file_density(las_file_path)
    if error:
        return DensityReport(file_path=las_file_path, status='error', error=error)

    num_points = len(las.points)
    ranges = compute_xy_range(las.header)
    area_m2 = ranges['x_range'] * ranges['y_range']
    density = compute_density(num_points, area_m2)

    return DensityReport(
        file_path=las_file_path,
        num_points=num_points,
        area_m2=area_m2,
        density_pts_per_m2=density,
        x_range_m=ranges['x_range'],
        y_range_m=ranges['y_range']
    )


from dataclasses import dataclass, field
from typing import Optional, Tuple, List, Union
import laspy

@dataclass
class CoordinateSystemReport:
    file_path: str
    las_version: Optional[str] = None
    point_format: Optional[int] = None
    crs: Optional[str] = None
    epsg_code: Optional[Union[int, str]] = None
    x_range: Optional[List[float]] = None
    y_range: Optional[List[float]] = None
    z_range: Optional[List[float]] = None
    coord_range_reasonable: Optional[bool] = None
    status: str = 'success'
    error: Optional[str] = None

def read_las_file_crs(las_file_path: str) -> Union[laspy.LasData, str]:
    try:
        return laspy.read(las_file_path), None
    except Exception as e:
        return None, str(e)

def extract_crs_info(header: laspy.LasHeader) -> Tuple[Optional[str], Optional[int]]:
    try:
        if hasattr(header, 'crs') and header.crs:
            return str(header.crs), header.crs.to_epsg()
        else:
            return 'Not available', None
    except Exception:
        return 'Error reading CRS', None

def compute_coord_ranges(header: laspy.LasHeader) -> Tuple[List[float], List[float], List[float], bool]:
    x_range = [header.min[0], header.max[0]]
    y_range = [header.min[1], header.max[1]]
    z_range = [header.min[2], header.max[2]]

    coord_ok = (
        abs(x_range[1] - x_range[0]) < 1e6 and
        abs(y_range[1] - y_range[0]) < 1e6 and
        abs(z_range[1] - z_range[0]) < 1e4
    )
    return x_range, y_range, z_range, coord_ok

def validate_coordinate_system(las_file_path: str) -> CoordinateSystemReport:
    las, error = read_las_file_crs(las_file_path)
    if error:
        return CoordinateSystemReport(file_path=las_file_path, status='error', error=error)

    header = las.header
    crs_str, epsg_code = extract_crs_info(header)
    x_range, y_range, z_range, coord_ok = compute_coord_ranges(header)

    return CoordinateSystemReport(
        file_path=las_file_path,
        las_version=str(header.version),
        point_format=header.point_format.id,
        crs=crs_str,
        epsg_code=epsg_code,
        x_range=x_range,
        y_range=y_range,
        z_range=z_range,
        coord_range_reasonable=coord_ok
    )

def check_spatial_extent(las_file_path, expected_bounds=None):
    """
    Check spatial extent and validate against expected bounds if provided.
    """
    try:
        las = laspy.read(las_file_path)

        # Get actual bounds
        actual_bounds = {
            'x_min': las.header.min[0],
            'x_max': las.header.max[0],
            'y_min': las.header.min[1],
            'y_max': las.header.max[1],
            'z_min': las.header.min[2],
            'z_max': las.header.max[2]
        }

        result = {
            'file_path': las_file_path,
            'bounds': actual_bounds,
            'status': 'success'
        }

        # Compare with expected bounds if provided
        if expected_bounds:
            bounds_check = all([
                expected_bounds['x_min'] <= actual_bounds['x_min'] <= expected_bounds['x_max'],
                expected_bounds['y_min'] <= actual_bounds['y_min'] <= expected_bounds['y_max'],
                actual_bounds['x_max'] <= expected_bounds['x_max'],
                actual_bounds['y_max'] <= expected_bounds['y_max']
            ])
            result['bounds_within_expected'] = bounds_check
            result['expected_bounds'] = expected_bounds

        return result

    except Exception as e:
        return {
            'file_path': las_file_path,
            'error': str(e),
            'status': 'error'
        }

from dataclasses import dataclass
from typing import Optional, Dict, Union
import laspy

@dataclass
class Bounds:
    x_min: float
    x_max: float
    y_min: float
    y_max: float
    z_min: float
    z_max: float

@dataclass
class SpatialExtentReport:
    file_path: str
    bounds: Optional[Bounds] = None
    expected_bounds: Optional[Dict[str, float]] = None
    bounds_within_expected: Optional[bool] = None
    status: str = 'success'
    error: Optional[str] = None

def extract_bounds(header: laspy.LasHeader) -> Bounds:
    return Bounds(
        x_min=header.min[0], x_max=header.max[0],
        y_min=header.min[1], y_max=header.max[1],
        z_min=header.min[2], z_max=header.max[2]
    )

def is_within_expected(actual: Bounds, expected: Dict[str, float]) -> bool:
    return all([
        expected['x_min'] <= actual.x_min <= expected['x_max'],
        expected['y_min'] <= actual.y_min <= expected['y_max'],
        actual.x_max <= expected['x_max'],
        actual.y_max <= expected['y_max']
    ])

def check_spatial_extent(las_file_path: str, expected_bounds: Optional[Dict[str, float]] = None) -> SpatialExtentReport:
    try:
        las = laspy.read(las_file_path)
        actual_bounds = extract_bounds(las.header)

        report = SpatialExtentReport(
            file_path=las_file_path,
            bounds=actual_bounds
        )

        if expected_bounds:
            report.expected_bounds = expected_bounds
            report.bounds_within_expected = is_within_expected(actual_bounds, expected_bounds)

        return report

    except Exception as e:
        return SpatialExtentReport(
            file_path=las_file_path,
            status='error',
            error=str(e)
        )

from dataclasses import dataclass
from typing import Optional, List
import numpy as np
import laspy

@dataclass
class OrientationReport:
    file_path: str
    z_statistics: Optional[dict] = None
    potential_issues: Optional[List[str]] = None
    status: str = 'success'
    error: Optional[str] = None

def check_orientation_and_ground(las_file_path: str) -> OrientationReport:
    try:
        las = laspy.read(las_file_path)
        z_values = las.z

        z_min = float(np.min(z_values))
        z_max = float(np.max(z_values))
        z_range = float(z_max - z_min)
        z_mean = float(np.mean(z_values))

        potential_issues = []
        if z_range > 100:  # example heuristic
            potential_issues.append("Large vertical range; might need normalization or classification.")

        return OrientationReport(
            file_path=las_file_path,
            z_statistics={
                'z_min': z_min,
                'z_max': z_max,
                'z_range': z_range,
                'z_mean': z_mean
            },
            potential_issues=potential_issues
        )

    except Exception as e:
        return OrientationReport(
            file_path=las_file_path,
            status='error',
            error=str(e)
        )


import os
import laspy
from dataclasses import dataclass, field
from typing import Optional, Dict, Union

@dataclass
class LASIntegrityReport:
    file_path: str
    integrity_checks: Dict[str, Union[bool, int, float, str]] = field(default_factory=dict)
    status: str = "success"
    error: Optional[str] = None

def file_exists_and_size(las_file_path: str) -> Union[int, str]:
    if not os.path.exists(las_file_path):
        return None, "File not found"
    return os.path.getsize(las_file_path), None

def read_las_header(las_file_path: str) -> Union[laspy.LasHeader, str]:
    try:
        las = laspy.read(las_file_path)
        return las.header, None, las
    except Exception as e:
        return None, str(e), None

def perform_integrity_checks(las: laspy.LasData, header: laspy.LasHeader, file_size: int) -> Dict:
    checks = {
        'file_readable': True,
        'file_size_bytes': file_size,
        'file_size_mb': round(file_size / (1024 * 1024), 2),
        'header_valid': True,
        'point_count_match': len(las.points) == header.point_count,
        'las_version': str(header.version),
        #'las_version': f"{header.version_major}.{header.version_minor}",
        'point_format': header.point_format.id
    }
    if file_size < 1024:
        checks['warning'] = 'File suspiciously small'
    return checks

def check_file_integrity(las_file_path: str) -> LASIntegrityReport:
    file_size, size_error = file_exists_and_size(las_file_path)
    if size_error:
        return LASIntegrityReport(
            file_path=las_file_path,
            status='error',
            error=size_error
        )

    header, read_error, las = read_las_header(las_file_path)
    if read_error:
        return LASIntegrityReport(
            file_path=las_file_path,
            status='error',
            error=read_error
        )

    checks = perform_integrity_checks(las, header, file_size)

    return LASIntegrityReport(
        file_path=las_file_path,
        integrity_checks=checks
    )

from dataclasses import dataclass, field
from typing import List, Dict, Optional

import laspy
import numpy as np
from dataclasses import dataclass, field
from typing import List, Dict, Union, Optional

@dataclass
class LASAttributeReport:
    file_path: str
    available_attributes: List[str] = field(default_factory=list)
    attribute_checks: Dict[str, bool] = field(default_factory=dict)
    attribute_statistics: Dict[str, Dict] = field(default_factory=dict)
    point_format_id: Optional[int] = None
    status: str = "success"
    error: Optional[str] = None

def read_las_file(las_file_path: str) -> Union[laspy.LasData, str]:
    try:
        las = laspy.read(las_file_path)
        return las, None
    except Exception as e:
        return None, str(e)

def get_available_attributes(las: laspy.LasData) -> List[str]:
    return list(las.point_format.dimension_names)

def check_standard_attributes(available_attrs: List[str]) -> Dict[str, bool]:
    return {
        'has_intensity': 'intensity' in available_attrs,
        'has_rgb': all(attr in available_attrs for attr in ['red', 'green', 'blue']),
        'has_classification': 'classification' in available_attrs,
        'has_return_info': 'return_number' in available_attrs,
        'has_gps_time': 'gps_time' in available_attrs,
        'has_scan_angle': 'scan_angle_rank' in available_attrs
    }

def compute_intensity_stats(intensity_data: np.ndarray) -> Dict:
    return {
        'min': int(np.min(intensity_data)),
        'max': int(np.max(intensity_data)),
        'mean': float(np.mean(intensity_data)),
        'non_zero_count': int(np.count_nonzero(intensity_data))
    }

def compute_rgb_stats(las: laspy.LasData) -> Dict:
    return {
        'red_range': [int(np.min(las.red)), int(np.max(las.red))],
        'green_range': [int(np.min(las.green)), int(np.max(las.green))],
        'blue_range': [int(np.min(las.blue)), int(np.max(las.blue))]
    }

def gather_attribute_statistics(las: laspy.LasData, attr_checks: Dict[str, bool]) -> Dict[str, Dict]:
    stats = {}
    if attr_checks.get('has_intensity'):
        stats['intensity'] = compute_intensity_stats(las.intensity)
    if attr_checks.get('has_rgb'):
        stats['rgb'] = compute_rgb_stats(las)
    return stats

def check_attribute_channels(las_file_path: str) -> LASAttributeReport:
    las, error = read_las_file(las_file_path)
    if error:
        return LASAttributeReport(
            file_path=las_file_path,
            status='error',
            error=error
        )

    available_attrs = get_available_attributes(las)
    attr_checks = check_standard_attributes(available_attrs)
    attr_stats = gather_attribute_statistics(las, attr_checks)

    return LASAttributeReport(
        file_path=las_file_path,
        available_attributes=available_attrs,
        attribute_checks=attr_checks,
        attribute_statistics=attr_stats,
        point_format_id=las.header.point_format.id
    )

def comprehensive_quality_check(las_file_path, expected_bounds=None):
    """
    Run all quality checks on a single LAS file.
    """
    if not LASPY_AVAILABLE:
        return {'error': 'laspy not available', 'status': 'error'}

    print(f"\nAnalyzing: {os.path.basename(las_file_path)}")

    results = {
        'file_path': las_file_path,
        'file_name': os.path.basename(las_file_path)
    }

    # Run all checks
    print("  - Checking point density...")
    results['density'] = analyze_point_density(las_file_path)

    print("  - Validating coordinate system...")
    results['coordinate_system'] = validate_coordinate_system(las_file_path)

    print("  - Checking spatial extent...")
    results['spatial_extent'] = check_spatial_extent(las_file_path, expected_bounds)

    print("  - Analyzing orientation & ground level...")
    results['orientation'] = check_orientation_and_ground(las_file_path)

    print("  - Checking file integrity...")
    results['integrity'] = check_file_integrity(las_file_path)

    print("  - Checking attribute channels...")
    results['attributes'] = check_attribute_channels(las_file_path)

    return results

import pandas as pd

def create_quality_summary(results_list):
    """
    Create a summary DataFrame from quality check results.
    Each row corresponds to one LAS file's quality check.
    """
    summary_data = []

    for result in results_list:
        # Skip if there was an error processing the file
        if not result or 'error' in result:
            continue

        file_name = result.get('file_name', 'Unknown')

        # Access DensityReport attributes directly
        density_info = result.get('density')
        num_points = density_info.num_points if density_info else 'N/A'
        density_pts_per_m2 = density_info.density_pts_per_m2 if density_info else 0
        area_m2 = density_info.area_m2 if density_info else 0


        attr_report = result.get('attributes')
        attr_checks = attr_report.attribute_checks if attr_report else {}

        # Access CoordinateSystemReport attributes directly
        coord_info = result.get('coordinate_system')
        coord_epsg = coord_info.epsg_code if coord_info else 'Unknown'

        # Access OrientationReport attributes directly
        orientation_info = result.get('orientation')
        z_stats = orientation_info.z_statistics if orientation_info else {}
        issues = len(orientation_info.potential_issues) if orientation_info and orientation_info.potential_issues else 0

        # Access LASIntegrityReport object and its attributes
        integrity_info = result.get('integrity')
        file_size_mb = integrity_info.integrity_checks.get('file_size_mb', 'N/A') if integrity_info and hasattr(integrity_info, 'integrity_checks') else 'N/A'


        row = {
            'File': file_name,
            'Points': num_points,
            'Density (pts/m²)': round(density_pts_per_m2, 2),
            'Area (m²)': round(area_m2, 2),
            'File Size (MB)': file_size_mb,
            'Has RGB': attr_checks.get('has_rgb', False),
            'Has Intensity': attr_checks.get('has_intensity', False),
            'EPSG Code': coord_epsg,
            'Z Range (m)': round(z_stats.get('z_range', 0), 2),
            'Issues': issues
        }

        # Check integrity status and add error information if not success
        if integrity_info and integrity_info.status != 'success':
            row['Integrity Status'] = integrity_info.status
            row['Integrity Error'] = integrity_info.error

        summary_data.append(row)

    return pd.DataFrame(summary_data)

from google.colab import drive
drive.mount("/content/drive", force_remount=True)

# Configuration
base_path = '/content/drive/MyDrive/thesis_datasets_20250613/thesis_datasets'
point_cloud_directory = f'{base_path}/pointcloud'
expected_bounds = None  # Optional: {'x_min': 0, 'x_max': 1000, 'y_min': 0, 'y_max': 1000}

# Find all LAS/LAZ files
las_files = []
if os.path.exists(point_cloud_directory):
    for ext in ['*.las', '*.laz', '*.LAS', '*.LAZ']:
        las_files.extend(Path(point_cloud_directory).rglob(ext))

    print(f"Found {len(las_files)} LAS/LAZ files")
    for f in las_files:
        print(f"  - {f}")
else:
    print(f"Directory not found: {point_cloud_directory}")
    print("Please update the point_cloud_directory path above")

# Run comprehensive quality assessment
if LASPY_AVAILABLE and las_files:
    all_results = []

    for las_file in las_files:
        try:
            result = comprehensive_quality_check(str(las_file), expected_bounds)
            all_results.append(result)
        except Exception as e:
            print(f"Error processing {las_file}: {e}")

    # Create summary
    if all_results:
        summary_df = create_quality_summary(all_results)
        print("\n" + "="*80)
        print("QUALITY ASSESSMENT SUMMARY")
        print("="*80)
        display(summary_df)

        # Save results
        summary_df.to_csv('point_cloud_quality_summary.csv', index=False)
        print("\nResults saved to: point_cloud_quality_summary.csv")
    else:
        print("No results to display")
else:
    print("Cannot run assessment: laspy not available or no LAS files found")